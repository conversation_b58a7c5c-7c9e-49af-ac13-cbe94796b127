"""
Callback query handlers
Maintains identical functionality to PHP version
"""

import logging
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes

from handlers.user_handlers import UserHandlers
from handlers.admin_handlers import AdminHandlers
from utils.helpers import is_admin, get_current_date
from config.settings import settings

logger = logging.getLogger(__name__)

class CallbackHandlers:
    """Handles callback queries from inline keyboards"""
    
    def __init__(self):
        self.user_handlers = UserHandlers()
        self.admin_handlers = AdminHandlers()
    
    async def handle_callback_query(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle callback queries (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id
        chat_id = query.message.chat_id
        message_id = query.message.message_id
        data = query.data
        first_name = query.from_user.first_name or ""
        last_name = query.from_user.last_name or ""
        
        try:
            # Check if this is a withdrawal approval/rejection callback (handled separately)
            is_withdrawal_callback = (
                data.startswith('approve_withdrawal_') or 
                data.startswith('reject_withdrawal_')
            )
            
            # Answer callback query to remove loading state (except for withdrawal callbacks)
            if not is_withdrawal_callback:
                await query.answer()
            
            # Handle different callback data
            if data == 'add':
                await self.admin_handlers.handle_add_balance(update, context)
            elif data == 'remove':
                await self.admin_handlers.handle_remove_balance(update, context)
            elif data == 'ban':
                await self.admin_handlers.handle_ban_user(update, context)
            elif data == 'unban':
                await self.admin_handlers.handle_unban_user(update, context)
            elif data == 'mainChannel':
                await self.admin_handlers.handle_set_main_channel(update, context)
            elif data == 'privateLogsChannel':
                await self.admin_handlers.handle_set_private_logs_channel(update, context)
            elif data == 'maintenanceStatus':
                await self.admin_handlers.handle_set_maintenance_status(update, context)
            elif data == 'OTPWebsiteAPIKey':
                await self.admin_handlers.handle_set_otp_api_key(update, context)
            elif data == 'perReferAmount':
                await self.admin_handlers.handle_set_per_refer_amount(update, context)
            elif data == 'joiningBonusAmount':
                await self.admin_handlers.handle_set_joining_bonus_amount(update, context)
            elif data == 'checkUserRecord':
                await self.admin_handlers.handle_check_user_record(update, context)
            elif data == 'passUserWithdrawal':
                await self.admin_handlers.handle_pass_user_withdrawal(update, context)
            elif data == 'failUserWithdrawal':
                await self.admin_handlers.handle_fail_user_withdrawal(update, context)
            elif data == 'broadcastGiftButton':
                await self.admin_handlers.handle_broadcast_gift_button(update, context)
            elif data == 'broadcastText':
                await self.admin_handlers.handle_broadcast_text(update, context)
            elif data == 'addForceSubChannel':
                await self.admin_handlers.handle_add_force_sub_channel(update, context)
            elif data == 'removeForceSubChannel':
                await self.admin_handlers.handle_remove_force_sub_channel(update, context)
            elif data == 'viewForceSubChannels':
                await self.admin_handlers.handle_view_force_sub_channels(update, context)
            elif data == 'withdrawal_settings':
                await self.admin_handlers.handle_withdrawal_settings(update, context)
            elif data == 'toggle_withdrawal_status':
                await self.admin_handlers.handle_toggle_withdrawal_status(update, context)
            elif data == 'configure_withdrawal_tax':
                await self.admin_handlers.handle_configure_withdrawal_tax(update, context)
            elif data == 'set_tax_none':
                await self.admin_handlers.handle_set_tax_type(update, context, 'none')
            elif data == 'set_tax_fixed':
                await self.admin_handlers.handle_set_tax_type(update, context, 'fixed')
            elif data == 'set_tax_percentage':
                await self.admin_handlers.handle_set_tax_type(update, context, 'percentage')
            elif data == 'preview_withdrawal_tax':
                await self._handle_preview_withdrawal_tax(update, context)
            elif data == 'preview_withdrawal_tax':
                await self.admin_handlers.handle_preview_withdrawal_tax(update, context)
            elif data == 'extraRewards':
                await self._handle_extra_rewards(update, context)
            elif data == 'taskRewards':
                await self._handle_task_rewards(update, context)
            elif data == 'redeemGiftCode':
                await self._handle_redeem_gift_code(update, context)
            elif data == 'generateGiftCode':
                await self.admin_handlers.handle_generate_gift_code(update, context)
            elif data == 'viewAllGiftCodes':
                await self._handle_view_all_gift_codes(update, context)
            elif data == 'manageTasks':
                await self.admin_handlers.handle_manage_tasks(update, context)
            elif data == 'addNewTask':
                await self.admin_handlers.handle_add_new_task(update, context)
            elif data.startswith('editTask_'):
                task_id = data.replace('editTask_', '')
                await self.admin_handlers.handle_edit_task(update, context, task_id)
            elif data == 'generateGiftCode':
                await self.admin_handlers.handle_generate_gift_code(update, context)
            elif data == 'resetUserAccount':
                await self.admin_handlers.handle_reset_user_account(update, context)
            elif data == 'viewPendingSubmissions':
                await self.admin_handlers.handle_view_pending_submissions(update, context)
            elif data == 'levelRewards':
                await self._handle_level_rewards(update, context)
            elif data.startswith('claimLevel_'):
                level = int(data.split('_')[1])
                await self._handle_claim_level_bonus(update, context, level)
            elif data == 'configureLevelRewards':
                await self.admin_handlers.handle_configure_level_rewards(update, context)
            elif data == 'toggleLevelBonus':
                await self.admin_handlers.handle_toggle_level_bonus(update, context)
            elif data == 'configureLevelSettings':
                await self._handle_configure_level_settings(update, context)
            elif data == 'admin':
                await self.admin_handlers.handle_admin_command(update, context)
            elif data == 'joined':
                await self.user_handlers.handle_joined_channel(update, context)
            elif data == 'myWallet':
                await self._handle_my_wallet(update, context)
            elif data == 'cashOut':
                await self._handle_cash_out(update, context)
            elif data == 'setAccountInfo':
                await self._handle_set_account_info(update, context)
            elif data == 'promotionReport':
                await self._handle_promotion_report(update, context)
            elif data == 'withdrawalRecord':
                await self._handle_withdrawal_record(update, context)
            elif data == 'changeWithdrawalMethod':
                await self._handle_change_withdrawal_method(update, context)
            elif data == 'claimBonus':
                await self._handle_claim_bonus(update, context)
            elif data == 'customReferralLinks':
                await self.admin_handlers.handle_custom_referral_links(update, context)
            elif data == 'withdrawal_method_bank':
                await self._handle_withdrawal_method_selection(update, context, 'bank')
            elif data == 'withdrawal_method_usdt':
                await self._handle_withdrawal_method_selection(update, context, 'usdt')
            elif data == 'manageWithdrawals':
                await self.admin_handlers.handle_manage_withdrawals(update, context)
            elif data == 'claimGift':
                await self._handle_claim_gift(update, context)
            elif data == 'addForceChannel':
                await self.admin_handlers.handle_add_force_sub_channel(update, context)
            elif data == 'removeForceChannel':
                await self.admin_handlers.handle_remove_force_sub_channel(update, context)
            elif data == 'viewForceChannels':
                await self.admin_handlers.handle_view_force_sub_channels(update, context)
            elif data == 'checkSubscription':
                await self._handle_check_subscription(update, context)
            elif data == 'extraRewards':
                await self.admin_handlers.handle_extra_rewards(update, context)
            elif data == 'viewAllGiftCodes':
                await self.admin_handlers.handle_view_all_gift_codes(update, context)
            elif data == 'analytics_overview':
                await self.admin_handlers.handle_analytics_overview(update, context)
            elif data == 'manageUserAccounts':
                await self.admin_handlers.handle_manage_user_accounts(update, context)
            elif data == 'viewUserDetails':
                await self.admin_handlers.handle_view_user_details(update, context)
            elif data == 'resetUserMethod':
                await self.admin_handlers.handle_reset_user_method(update, context)

            # ==================== NEW USER BONUS MANAGEMENT CALLBACKS ====================
            elif data == 'user_bonus':
                await self.admin_handlers.handle_user_bonus(update, context)
            elif data == 'toggle_level_bonus':
                await self.admin_handlers.handle_toggle_level_bonus(update, context)
            elif data == 'set_new_user_bonus':
                await self.admin_handlers.handle_set_new_user_bonus(update, context)
            elif data == 'set_invite_bonus':
                await self.admin_handlers.handle_set_invite_bonus(update, context)
            elif data == 'configure_level_rewards':
                await self.admin_handlers.handle_configure_level_rewards(update, context)

            # ==================== NEW USER DETAILS & SETTINGS CALLBACKS ====================
            elif data == 'user_details_settings':
                await self.admin_handlers.handle_user_details_settings(update, context)
            elif data == 'admin_panel':
                await self.admin_handlers.handle_admin_command(update, context)

            # ==================== TASK MANAGEMENT CALLBACKS ====================
            elif data == 'manage_tasks':
                await self.admin_handlers.handle_manage_tasks(update, context)
            elif data == 'add_task':
                await self.admin_handlers.handle_add_task(update, context)

            # ==================== ADMIN MANAGEMENT CALLBACKS ====================
            elif data == 'manage_admins':
                await self.admin_handlers.handle_manage_admins(update, context)
            elif data == 'add_admin':
                await self.admin_handlers.handle_add_admin(update, context)
            elif data == 'remove_admin':
                await self.admin_handlers.handle_remove_admin(update, context)

            # ==================== USER BROADCAST CALLBACKS ====================
            elif data == 'user_broadcast':
                await self.admin_handlers.handle_user_broadcast(update, context)
            elif data == 'broadcast_media':
                await self.admin_handlers.handle_broadcast_media(update, context)
            elif data == 'broadcast_text':
                await self.admin_handlers.handle_broadcast_text(update, context)
            elif data == 'broadcast_buttons':
                await self.admin_handlers.handle_broadcast_buttons(update, context)
            elif data == 'broadcast_preview':
                await self.admin_handlers.handle_broadcast_preview(update, context)
            elif data == 'start_broadcast':
                await self.admin_handlers.handle_start_broadcast(update, context)
            elif data == 'confirm_broadcast':
                await self.admin_handlers.handle_confirm_broadcast(update, context)

            # ==================== BOT STATISTICS & ANALYSIS CALLBACKS ====================
            elif data == 'bot_statistics':
                await self.admin_handlers.handle_bot_statistics(update, context)
            elif data == 'overall_statistics':
                await self.admin_handlers.handle_overall_statistics(update, context)
            elif data == 'top_balances':
                await self.admin_handlers.handle_top_balances(update, context)
            elif data == 'top_inviters':
                await self.admin_handlers.handle_top_inviters(update, context)
            elif data == 'top_withdrawers':
                await self.admin_handlers.handle_top_withdrawers(update, context)
            elif data == 'channel_stats':
                await self.admin_handlers.handle_channel_stats(update, context)
            elif data == 'pending_withdrawals':
                await self.admin_handlers.handle_pending_withdrawals(update, context)
            elif data == 'user_history':
                await self.admin_handlers.handle_user_history(update, context)

            # ==================== GIFT CODE MANAGEMENT CALLBACKS ====================
            elif data == 'manage_gift_codes':
                await self.admin_handlers.handle_manage_gift_codes(update, context)
            elif data == 'add_redeem_code':
                await self.admin_handlers.handle_add_redeem_code(update, context)
            elif data == 'live_codes':
                await self.admin_handlers.handle_live_codes(update, context)
            elif data == 'used_codes':
                await self.admin_handlers.handle_used_codes(update, context)
            elif data == 'link_based_redeem':
                await self.admin_handlers.handle_link_based_redeem(update, context)
            elif data == 'link_fixed_amount':
                await self.admin_handlers.handle_link_fixed_amount(update, context)
            elif data == 'link_random_amount':
                await self.admin_handlers.handle_link_random_amount(update, context)
            elif data == 'link_user_limit':
                await self.admin_handlers.handle_link_user_limit(update, context)
            elif data == 'generate_redeem_link':
                await self.admin_handlers.handle_generate_redeem_link(update, context)

            # ==================== FORCESUB CHANNELS MANAGEMENT CALLBACKS ====================
            elif data == 'forcesub_channels':
                await self.admin_handlers.handle_forcesub_channels(update, context)
            elif data == 'set_main_channel':
                await self.admin_handlers.handle_set_main_channel(update, context)
            elif data == 'add_forcesub_channel':
                await self.admin_handlers.handle_add_forcesub_channel(update, context)
            elif data == 'remove_forcesub_channel':
                await self.admin_handlers.handle_remove_forcesub_channel(update, context)

            # ==================== GIFT BROADCAST CALLBACKS ====================
            elif data == 'gift_broadcast':
                await self.admin_handlers.handle_gift_broadcast(update, context)

            # ==================== BOT MAINTENANCE CALLBACKS ====================
            elif data == 'bot_maintenance':
                await self.admin_handlers.handle_bot_maintenance(update, context)
            elif data == 'toggle_general_maintenance':
                await self.admin_handlers.handle_toggle_general_maintenance(update, context)
            elif data == 'withdrawal_maintenance':
                await self.admin_handlers.handle_withdrawal_maintenance(update, context)
            elif data == 'toggle_bank_maintenance':
                await self.admin_handlers.handle_toggle_bank_maintenance(update, context)
            elif data == 'toggle_usdt_maintenance':
                await self.admin_handlers.handle_toggle_usdt_maintenance(update, context)

            # ==================== CUSTOM REFERRAL LINK CALLBACKS ====================
            elif data == 'custom_referral_link':
                await self.admin_handlers.handle_custom_referral_link(update, context)
            elif data == 'toggle_custom_referral':
                await self.admin_handlers.handle_toggle_custom_referral(update, context)
            elif data == 'delete_invite_link':
                await self.admin_handlers.handle_delete_invite_link(update, context)
            elif data == 'copy_admin_link':
                await self.admin_handlers.handle_copy_admin_link(update, context)

            # ==================== NEW ENHANCED ADMIN PANEL CALLBACKS ====================
            elif data == 'manage_withdrawals':
                await self.admin_handlers.handle_manage_withdrawals(update, context)
            elif data == 'toggle_withdrawal_status':
                await self.admin_handlers.handle_toggle_withdrawal_status(update, context)
            elif data == 'set_percent_tax':
                await self.admin_handlers.handle_set_percent_tax(update, context)
            elif data == 'set_fixed_tax':
                await self.admin_handlers.handle_set_fixed_tax(update, context)
            elif data == 'toggle_unique_accounts':
                await self.admin_handlers.handle_toggle_unique_accounts(update, context)
            elif data == 'ban_user_withdrawal':
                await self.admin_handlers.handle_ban_user_withdrawal(update, context)
            elif data == 'change_user_cashout':
                await self.admin_handlers.handle_change_user_cashout(update, context)
            elif data == 'reject_all_withdrawals':
                await self.admin_handlers.handle_reject_all_withdrawals(update, context)
            elif data == 'confirm_reject_all_withdrawals':
                await self.admin_handlers.handle_confirm_reject_all_withdrawals(update, context)

            # Original Admin Panel Callbacks Only

            # IFSC Validation and Bank Details Callbacks
            elif data.startswith('confirm_ifsc_'):
                await self._handle_confirm_ifsc(update, context)
            elif data == 'reenter_ifsc':
                await self._handle_reenter_ifsc(update, context)
            elif data == 'retry_ifsc':
                await self._handle_retry_ifsc(update, context)
            elif data == 'manual_bank_entry':
                await self._handle_manual_bank_entry(update, context)
            else:
                # Handle dynamic callback data
                await self._handle_dynamic_callbacks(update, context, data)
                
        except Exception as e:
            logger.error(f"Error in callback handler for data '{data}': {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)
    
    async def _handle_dynamic_callbacks(self, update: Update, context: ContextTypes.DEFAULT_TYPE, data: str) -> None:
        """Handle dynamic callback data with parameters"""
        query = update.callback_query
        
        try:
            # Handle withdrawal amounts
            if data.startswith('withdraw '):
                amount = int(data.replace('withdraw ', ''))
                await self._handle_withdraw_amount(update, context, amount)
            
            # Handle account info setting
            elif data.startswith('set '):
                field = data.replace('set ', '')
                await self._handle_set_account_field(update, context, field)
            
            # Handle remove force sub channel
            elif data.startswith('remove_force_sub_'):
                channel_id = data.replace('remove_force_sub_', '')
                await self.admin_handlers.handle_remove_force_sub_channel_confirm(update, context, channel_id)
            
            # Handle view task
            elif data.startswith('viewTask_'):
                task_id = data.replace('viewTask_', '')
                await self._handle_view_task(update, context, task_id)
            
            # Handle submit task
            elif data.startswith('submitTask_'):
                task_id = data.replace('submitTask_', '')
                await self._handle_submit_task(update, context, task_id)
            
            # Handle approve task
            elif data.startswith('approveTask_'):
                submission_id = data.replace('approveTask_', '')
                await self.admin_handlers.handle_approve_task_submission(update, context, submission_id)
            
            # Handle reject task
            elif data.startswith('rejectTask_'):
                submission_id = data.replace('rejectTask_', '')
                await self.admin_handlers.handle_reject_task_submission(update, context, submission_id)
            
            # Handle set task status
            elif data.startswith('setTaskStatus_'):
                status = data.replace('setTaskStatus_', '')
                await self.admin_handlers.handle_add_task_step6(update, context, status)

            # Handle task editing operations
            elif data.startswith('editTaskName_'):
                task_id = data.replace('editTaskName_', '')
                await self.admin_handlers.handle_edit_task_name(update, context, task_id)
            elif data.startswith('editTaskDesc_'):
                task_id = data.replace('editTaskDesc_', '')
                await self.admin_handlers.handle_edit_task_description(update, context, task_id)
            elif data.startswith('editTaskReward_'):
                task_id = data.replace('editTaskReward_', '')
                await self.admin_handlers.handle_edit_task_reward(update, context, task_id)
            elif data.startswith('editTaskMedia_'):
                task_id = data.replace('editTaskMedia_', '')
                await self.admin_handlers.handle_edit_task_media(update, context, task_id)
            elif data.startswith('toggleTaskStatus_'):
                task_id = data.replace('toggleTaskStatus_', '')
                await self.admin_handlers.handle_toggle_task_status(update, context, task_id)
            elif data.startswith('deleteTask_'):
                task_id = data.replace('deleteTask_', '')
                await self.admin_handlers.handle_delete_task_confirm(update, context, task_id)
            elif data.startswith('confirmDeleteTask_'):
                task_id = data.replace('confirmDeleteTask_', '')
                await self.admin_handlers.handle_delete_task(update, context, task_id)
            elif data.startswith('confirmResetAccount_'):
                target_user_id = int(data.replace('confirmResetAccount_', ''))
                await self._handle_confirm_reset_account(update, context, target_user_id)

            # Handle claim level bonus
            elif data.startswith('claimLevel_'):
                level = int(data.replace('claimLevel_', ''))
                await self._handle_claim_level_bonus(update, context, level)
            
            # Handle withdrawal approval
            elif data.startswith('approve_withdrawal_'):
                target_user_id = data.replace('approve_withdrawal_', '')
                await self._handle_withdrawal_approval_callback(update, context, target_user_id, 'approve')
            
            # Handle withdrawal rejection
            elif data.startswith('reject_withdrawal_'):
                target_user_id = data.replace('reject_withdrawal_', '')
                await self._handle_withdrawal_approval_callback(update, context, target_user_id, 'reject')
            
            # Handle withdrawal amount selection
            elif data.startswith('withdraw '):
                amount = int(data.replace('withdraw ', ''))
                await self._handle_withdraw_amount(update, context, amount)

            # Handle account field setting
            elif data.startswith('set '):
                field = data.replace('set ', '')
                await self._handle_set_account_field(update, context, field)

            # Handle custom referral callbacks
            elif data.startswith('customref_'):
                action = data.replace('customref_', '')
                await self.admin_handlers.handle_custom_referral_callback(update, context, action)

            # Handle analytics callbacks
            elif data.startswith('analytics_'):
                action = data.replace('analytics_', '')
                await self.admin_handlers.handle_analytics_callback(update, context, action)

            # ==================== USER MANAGEMENT DYNAMIC CALLBACKS ====================
            # Handle user ban/unban callbacks
            elif data.startswith('ban_user_'):
                target_user_id = int(data.replace('ban_user_', ''))
                await self.admin_handlers.handle_user_ban_toggle(update, context, target_user_id, True)
            elif data.startswith('unban_user_'):
                target_user_id = int(data.replace('unban_user_', ''))
                await self.admin_handlers.handle_user_ban_toggle(update, context, target_user_id, False)

            # Handle show user details
            elif data.startswith('show_user_'):
                target_user_id = int(data.replace('show_user_', ''))
                await self.admin_handlers.handle_show_user_details(update, context, target_user_id)

            # Handle user invites record with pagination
            elif data.startswith('user_invites_'):
                parts = data.replace('user_invites_', '').split('_')
                target_user_id = int(parts[0])
                page = int(parts[1]) if len(parts) > 1 else 1
                await self.admin_handlers.handle_user_invites_record(update, context, target_user_id, page)

            # Handle user balance operations
            elif data.startswith('add_balance_'):
                target_user_id = int(data.replace('add_balance_', ''))
                await self.admin_handlers.handle_user_balance_operation(update, context, target_user_id, 'add')
            elif data.startswith('remove_balance_'):
                target_user_id = int(data.replace('remove_balance_', ''))
                await self.admin_handlers.handle_user_balance_operation(update, context, target_user_id, 'remove')

            # Handle send message to user
            elif data.startswith('send_message_'):
                target_user_id = int(data.replace('send_message_', ''))
                await self.admin_handlers.handle_send_user_message(update, context, target_user_id)

            # Handle user task records
            elif data.startswith('user_tasks_'):
                target_user_id = int(data.replace('user_tasks_', ''))
                await self.admin_handlers.handle_user_task_records(update, context, target_user_id)

            # Handle user withdrawal records (placeholder for now)
            elif data.startswith('user_withdrawals_'):
                target_user_id = int(data.replace('user_withdrawals_', ''))
                # TODO: Implement withdrawal records display
                await update.callback_query.answer("🚧 Withdrawal records feature coming soon!", show_alert=True)

            # ==================== TASK MANAGEMENT DYNAMIC CALLBACKS ====================
            # Handle individual task management
            elif data.startswith('manage_task_'):
                task_id = data.replace('manage_task_', '')
                await self.admin_handlers.handle_manage_individual_task(update, context, task_id)

            # Handle task editing callbacks
            elif data.startswith('edit_task_name_'):
                task_id = data.replace('edit_task_name_', '')
                await self.admin_handlers.handle_edit_task_name(update, context, task_id)
            elif data.startswith('edit_task_bonus_'):
                task_id = data.replace('edit_task_bonus_', '')
                await self.admin_handlers.handle_edit_task_bonus(update, context, task_id)
            elif data.startswith('edit_task_content_'):
                task_id = data.replace('edit_task_content_', '')
                await self.admin_handlers.handle_edit_task_content(update, context, task_id)
            elif data.startswith('edit_task_image_'):
                task_id = data.replace('edit_task_image_', '')
                await self.admin_handlers.handle_edit_task_image(update, context, task_id)
            elif data.startswith('delete_task_'):
                task_id = data.replace('delete_task_', '')
                await self.admin_handlers.handle_delete_task(update, context, task_id)

            # ==================== STATISTICS HISTORY DYNAMIC CALLBACKS ====================
            # Handle history pagination callbacks
            elif data.startswith('history_'):
                # Parse history callback: history_type_page
                parts = data.split('_')
                if len(parts) >= 3:
                    try:
                        # Extract history type and page
                        history_type = '_'.join(parts[1:-1])  # Everything except 'history' and page number
                        page = int(parts[-1])

                        await self.admin_handlers.handle_user_history_detail(update, context, history_type, page)
                    except (ValueError, IndexError) as e:
                        logger.error(f"Error parsing history callback {data}: {e}")
                        await update.callback_query.answer("❌ Invalid history request.", show_alert=True)

            # ==================== GIFT BROADCAST VERIFICATION CALLBACKS ====================
            # Handle gift broadcast verification callbacks
            elif data.startswith('verify_gift_broadcast_'):
                # Parse broadcast ID: verify_gift_broadcast_[broadcast_id]
                broadcast_id = data.replace('verify_gift_broadcast_', '')
                await self._handle_gift_broadcast_verification(update, context, broadcast_id)

            else:
                # Unknown callback data
                await query.answer("❌ Unknown action.", show_alert=True)
                
        except Exception as e:
            logger.error(f"Error handling dynamic callback '{data}': {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)
    
    # Placeholder methods for user-related callbacks
    # These will be implemented as we build out the respective services
    
    async def _handle_my_wallet(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle my wallet callback (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id
        chat_id = query.message.chat_id
        message_id = query.message.message_id

        try:
            # Check maintenance and ban status
            from services.user_service import UserService
            user_service = UserService()

            if await user_service.is_maintenance_mode():
                await query.edit_message_text(
                    "⚙️ Bot is currently under maintenance, please try again later.",
                    parse_mode='HTML'
                )
                return

            user = await user_service.get_user(user_id)
            if not user:
                await query.edit_message_text(
                    "❌ <b>User Not Found</b>\n\nPlease send /start first.",
                    parse_mode='HTML'
                )
                return

            if user.get('banned', False):
                await query.edit_message_text(
                    "🚫 You are banned from using this bot.",
                    parse_mode='HTML'
                )
                return

            # Get withdrawal method and display status (matching PHP exactly)
            account_info = user.get('account_info', {})
            withdrawal_method = account_info.get('withdrawal_method', 'bank')

            if withdrawal_method == 'usdt':
                method_status = "✅ USDT (Binance ID)"
            else:
                method_status = "✅ Bank Account"

            # Get user data
            balance = user.get('balance', 0)
            successful_withdraw = user.get('successful_withdraw', 0)
            withdraw_under_review = user.get('withdraw_under_review', 0)

            # Format wallet message exactly like PHP
            wallet_message = "💰 <b>My Wallet</b>\n\n"
            wallet_message += f"💵 <b>Balance:</b> ₹{balance}\n"
            wallet_message += f"✅ <b>Successful Withdraw:</b> ₹{successful_withdraw}\n"
            wallet_message += f"⏳ <b>Under Review:</b> ₹{withdraw_under_review}\n\n"
            wallet_message += f"🔧 <b>Withdrawal Method:</b> {method_status}"

            from telegram import InlineKeyboardButton, InlineKeyboardMarkup

            # Create wallet keyboard exactly like PHP
            wallet_keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('🏧 Cash out', callback_data='cashOut')],
                [InlineKeyboardButton('⚙️ Set account info', callback_data='setAccountInfo')],
                [InlineKeyboardButton('📊 Promotion report', callback_data='promotionReport')],
                [InlineKeyboardButton('📝 Withdrawal record', callback_data='withdrawalRecord')],
                [InlineKeyboardButton('🎁 Extra Rewards', callback_data='extraRewards')],
                [InlineKeyboardButton('↩️ Back', callback_data='joined')]
            ])

            # Use smart navigation to handle media-to-text transitions
            await self._smart_navigate_to_text_message(query, wallet_message, wallet_keyboard)

        except Exception as e:
            logger.error(f"Error in _handle_my_wallet: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)
    
    async def _handle_cash_out(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle cash out callback (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id
        chat_id = query.message.chat_id
        message_id = query.message.message_id

        try:
            # Check maintenance and ban status
            from services.user_service import UserService
            from services.maintenance_service import MaintenanceService

            user_service = UserService()
            maintenance_service = MaintenanceService()

            # Check general maintenance first
            if await maintenance_service.is_general_maintenance_active():
                await query.edit_message_text(
                    "⚙️ <b>System Under Maintenance</b>\n\n"
                    "The bot is currently under maintenance. Please try again later.\n\n"
                    "Thank you for your patience! 🙏",
                    parse_mode='HTML'
                )
                return

            user = await user_service.get_user(user_id)
            if not user:
                await query.edit_message_text(
                    "❌ <b>User Not Found</b>\n\nPlease send /start first.",
                    parse_mode='HTML'
                )
                return

            if user.get('banned', False):
                await query.edit_message_text(
                    "🚫 You are banned from using this bot.",
                    parse_mode='HTML'
                )
                return

            # Check if user has pending withdrawal
            if user.get('withdraw_under_review', 0) > 0:
                pending_amount = user.get('withdraw_under_review', 0)
                message = f"⏳ <b>Withdrawal Pending</b>\n\n"
                message += f"You already have a withdrawal request of ₹{pending_amount} under review.\n\n"
                message += "Please wait for the current request to be processed before making a new one."

                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to Wallet', callback_data='myWallet')]
                ])

                await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')
                return

            # Check minimum balance
            balance = user.get('balance', 0)
            min_withdrawal = settings.MIN_WITHDRAWAL_AMOUNT

            if balance < min_withdrawal:
                message = f"💸 <b>Cash Out</b>\n\n"
                message += f"❌ Insufficient balance for withdrawal.\n\n"
                message += f"💰 <b>Your Balance:</b> ₹{balance}\n"
                message += f"💵 <b>Minimum Withdrawal:</b> ₹{min_withdrawal}\n\n"
                message += f"You need at least ₹{min_withdrawal - balance} more to make a withdrawal."

                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('🎁 Earn More', callback_data='extraRewards')],
                    [InlineKeyboardButton('↩️ Back to Wallet', callback_data='myWallet')]
                ])

                await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')
                return

            # Check account information
            from services.withdrawal_service import WithdrawalService
            from models.withdrawal import AccountInfoModel

            withdrawal_service = WithdrawalService()
            account_info = user.get('account_info', {})
            withdrawal_method = account_info.get('withdrawal_method', 'bank')

            # Check specific withdrawal type maintenance
            if withdrawal_method == 'usdt' and await maintenance_service.is_usdt_withdrawal_maintenance_active():
                await query.edit_message_text(
                    "⚙️ <b>USDT Withdrawals Under Maintenance</b>\n\n"
                    "USDT withdrawals are currently under maintenance. Please try again later.\n\n"
                    "💡 You can switch to bank withdrawals in your account settings if needed.\n\n"
                    "Thank you for your patience! 🙏",
                    parse_mode='HTML'
                )
                return
            elif withdrawal_method == 'bank' and await maintenance_service.is_bank_withdrawal_maintenance_active():
                await query.edit_message_text(
                    "⚙️ <b>Bank Withdrawals Under Maintenance</b>\n\n"
                    "Bank withdrawals are currently under maintenance. Please try again later.\n\n"
                    "💡 You can switch to USDT withdrawals in your account settings if needed.\n\n"
                    "Thank you for your patience! 🙏",
                    parse_mode='HTML'
                )
                return

            if withdrawal_method == 'usdt':
                account_validation = AccountInfoModel.validate_usdt_account_info(account_info)
            else:
                account_validation = AccountInfoModel.validate_bank_account_info(account_info)

            if not account_validation['valid']:
                message = f"💸 <b>Cash Out</b>\n\n"
                message += f"❌ Please complete your account information first.\n\n"
                message += f"💰 <b>Your Balance:</b> ₹{balance}\n\n"
                message += "You need to set up your payment details before making a withdrawal."

                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('⚙️ Set Account Info', callback_data='setAccountInfo')],
                    [InlineKeyboardButton('↩️ Back to Wallet', callback_data='myWallet')]
                ])

                await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')
                return

            # Show withdrawal amounts
            available_amounts = await withdrawal_service.get_withdrawal_amounts(balance)

            if not available_amounts:
                message = f"💸 <b>Cash Out</b>\n\n"
                message += f"❌ No withdrawal amounts available.\n\n"
                message += f"💰 <b>Your Balance:</b> ₹{balance}\n"
                message += f"💵 <b>Minimum Withdrawal:</b> ₹{min_withdrawal}"

                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to Wallet', callback_data='myWallet')]
                ])

                await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')
                return

            message = f"💸 <b>Cash Out</b>\n\n"
            message += f"💰 <b>Your Balance:</b> ₹{balance}\n"
            message += f"🏦 <b>Withdrawal Method:</b> {'USDT (Binance ID)' if withdrawal_method == 'usdt' else 'Bank Account'}\n\n"
            message += "💵 <b>Select withdrawal amount:</b>"

            # Create withdrawal amount buttons
            keyboard_buttons = []
            for amount in available_amounts:
                keyboard_buttons.append([
                    InlineKeyboardButton(f"₹{amount}", callback_data=f"withdraw {amount}")
                ])

            keyboard_buttons.append([
                InlineKeyboardButton('↩️ Back to Wallet', callback_data='myWallet')
            ])

            keyboard = InlineKeyboardMarkup(keyboard_buttons)

            # Use smart navigation to handle media-to-text transitions
            await self._smart_navigate_to_text_message(query, message, keyboard)

        except Exception as e:
            logger.error(f"Error in _handle_cash_out: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)
    
    async def _handle_set_account_info(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle set account info callback (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id
        chat_id = query.message.chat_id
        message_id = query.message.message_id

        try:
            # Check maintenance and ban status
            from services.user_service import UserService
            user_service = UserService()

            if await user_service.is_maintenance_mode():
                await query.edit_message_text(
                    "⚙️ Bot is currently under maintenance, please try again later.",
                    parse_mode='HTML'
                )
                return

            user = await user_service.get_user(user_id)
            if not user:
                await query.edit_message_text(
                    "❌ <b>User Not Found</b>\n\nPlease send /start first.",
                    parse_mode='HTML'
                )
                return

            if user.get('banned', False):
                await query.edit_message_text(
                    "🚫 You are banned from using this bot.",
                    parse_mode='HTML'
                )
                return

            # Show account info setup menu
            from models.withdrawal import AccountInfoModel

            account_info = user.get('account_info', {})
            withdrawal_method = account_info.get('withdrawal_method', 'bank')

            message = AccountInfoModel.format_account_info_message(user)

            # Create keyboard based on withdrawal method (matching PHP exactly)
            if withdrawal_method == 'usdt':
                keyboard_buttons = [
                    [InlineKeyboardButton('₿ Set Binance ID', callback_data='set USDTAddress')],
                    [InlineKeyboardButton('🔄 Change Method', callback_data='changeWithdrawalMethod')],
                    [InlineKeyboardButton('↩️ Back', callback_data='myWallet')]
                ]
            else:
                # Bank account layout exactly like PHP
                keyboard_buttons = [
                    [
                        InlineKeyboardButton('👤Name', callback_data='set Name'),
                        InlineKeyboardButton('ℹ️IFSC', callback_data='set IFSC'),
                        InlineKeyboardButton('📧Email', callback_data='set Email')
                    ],
                    [InlineKeyboardButton('💳Account Number', callback_data='set AccountNumber')],
                    [InlineKeyboardButton('📱Mobile Number', callback_data='set MobileNumber')],
                    [InlineKeyboardButton('🔄 Change Method', callback_data='changeWithdrawalMethod')],
                    [InlineKeyboardButton('↩️ Back', callback_data='myWallet')]
                ]

            keyboard = InlineKeyboardMarkup(keyboard_buttons)

            # Use smart navigation to handle media-to-text transitions
            await self._smart_navigate_to_text_message(query, message, keyboard)

        except Exception as e:
            logger.error(f"Error in _handle_set_account_info: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def _handle_change_withdrawal_method(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle change withdrawal method callback (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            # Check maintenance and ban status
            from services.user_service import UserService
            user_service = UserService()

            if await user_service.is_maintenance_mode():
                await query.edit_message_text(
                    "⚙️ Bot is currently under maintenance, please try again later.",
                    parse_mode='HTML'
                )
                return

            user = await user_service.get_user(user_id)
            if not user:
                await query.edit_message_text(
                    "❌ <b>User Not Found</b>\n\nPlease send /start first.",
                    parse_mode='HTML'
                )
                return

            if user.get('banned', False):
                await query.edit_message_text(
                    "🚫 You are banned from using this bot.",
                    parse_mode='HTML'
                )
                return

            # Allow users to change withdrawal method at any time
            account_info = user.get('account_info', {})
            current_method = account_info.get('withdrawal_method', 'bank')

            # Show current method and allow changes
            message = "🔄 <b>Change Withdrawal Method</b>\n\n"

            # Show current method if set
            if current_method:
                current_method_name = 'Bank Account' if current_method == 'bank' else 'USDT (Binance ID)'
                message += f"📋 <b>Current Method:</b> {current_method_name}\n\n"

                # Show current details summary
                if current_method == 'bank':
                    has_details = any([
                        account_info.get('name'),
                        account_info.get('account_number'),
                        account_info.get('ifsc'),
                        account_info.get('email')
                    ])
                    if has_details:
                        message += "🏦 <b>Current Bank Details:</b>\n"
                        message += f"• Name: {account_info.get('name', 'Not set')}\n"
                        message += f"• Account: {account_info.get('account_number', 'Not set')}\n"
                        message += f"• IFSC: {account_info.get('ifsc', 'Not set')}\n"
                        message += f"• Email: {account_info.get('email', 'Not set')}\n\n"
                else:
                    binance_id = account_info.get('binance_id', account_info.get('usdt_address', ''))
                    if binance_id:
                        message += "₿ <b>Current USDT Details:</b>\n"
                        message += f"• Binance ID: {binance_id}\n\n"

            message += "Choose your preferred withdrawal method:\n\n"
            message += "🏦 <b>Bank Account</b>\n"
            message += "• Direct bank transfer\n"
            message += "• Requires bank details\n\n"
            message += "₿ <b>USDT (Binance ID)</b>\n"
            message += "• Cryptocurrency withdrawal\n"
            message += "• Requires Binance ID\n\n"
            message += "✅ <b>Note:</b> You can change your withdrawal method anytime.\n"
            message += "💡 <b>Tip:</b> Your existing details will be preserved when switching methods."

            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('🏦 Bank Account', callback_data='withdrawal_method_bank')],
                [InlineKeyboardButton('₿ USDT (Binance ID)', callback_data='withdrawal_method_usdt')],
                [InlineKeyboardButton('↩️ Back to Account Info', callback_data='setAccountInfo')]
            ])

            await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in _handle_change_withdrawal_method: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    # ==================== IFSC VALIDATION AND BANK DETAILS HANDLERS ====================

    async def _handle_confirm_ifsc(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle IFSC confirmation after bank details are fetched"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            # Extract IFSC from callback data
            ifsc_code = query.data.replace('confirm_ifsc_', '')

            # Get session data with bank details
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            session_data = await session_handlers.get_user_session(user_id)

            if not session_data or 'bank_details' not in session_data:
                await query.edit_message_text(
                    "❌ <b>Session Expired</b>\n\n"
                    "Please start the account setup process again.",
                    parse_mode='HTML'
                )
                return

            bank_details = session_data['bank_details']

            # Update account info with IFSC and bank details
            from services.withdrawal_service import WithdrawalService
            withdrawal_service = WithdrawalService()

            # Store IFSC and bank information
            account_updates = {
                'ifsc': ifsc_code,
                'bank_name': bank_details.get('bank_name'),
                'branch_name': bank_details.get('branch_name'),
                'bank_city': bank_details.get('city'),
                'bank_state': bank_details.get('state'),
                'bank_address': bank_details.get('address')
            }

            success = True
            for field, value in account_updates.items():
                if not await withdrawal_service.update_account_info(user_id, field, value):
                    success = False
                    break

            if success:
                # Clear session
                await session_handlers.clear_user_session(user_id)

                # Show success message and next steps
                success_message = "✅ <b>Bank Details Confirmed</b>\n"
                success_message += "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n"
                success_message += f"🏛️ <b>Bank:</b> {bank_details['bank_name']}\n"
                success_message += f"🏢 <b>Branch:</b> {bank_details['branch_name']}\n"
                success_message += f"🏛️ <b>IFSC:</b> {ifsc_code}\n\n"
                success_message += "✅ Your bank details have been saved successfully.\n\n"
                success_message += "📝 <b>Next Steps:</b>\n"
                success_message += "• Enter your account number\n"
                success_message += "• Provide your name as per bank records\n"
                success_message += "• Add email and mobile number"

                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('📝 Continue Setup', callback_data='setAccountInfo')],
                    [InlineKeyboardButton('💰 My Wallet', callback_data='myWallet')]
                ])

                await query.edit_message_text(success_message, reply_markup=keyboard, parse_mode='HTML')
            else:
                await query.edit_message_text(
                    "❌ <b>Failed to Save Bank Details</b>\n\n"
                    "Please try again or contact support.",
                    parse_mode='HTML'
                )

        except Exception as e:
            logger.error(f"Error in _handle_confirm_ifsc: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def _handle_reenter_ifsc(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle re-entering IFSC code"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            # Set session for IFSC entry
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'set_account_ifsc')

            message = "🏛️ <b>Enter IFSC Code</b>\n"
            message += "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n"
            message += "Please enter your bank's IFSC code.\n\n"
            message += "📝 <b>Format:</b> 11 characters (4 letters + 7 alphanumeric)\n"
            message += "💡 <b>Example:</b> SBIN0001234\n\n"
            message += "🔍 <b>Where to find IFSC:</b>\n"
            message += "• Bank passbook\n"
            message += "• Cheque book\n"
            message += "• Bank's official website\n\n"
            message += "Send /cancel to cancel this process."

            await query.edit_message_text(message, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in _handle_reenter_ifsc: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def _handle_retry_ifsc(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle retrying IFSC validation"""
        await self._handle_reenter_ifsc(update, context)

    async def _handle_manual_bank_entry(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle manual bank details entry (fallback)"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            # Set session for manual IFSC entry
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'set_account_ifsc_manual')

            message = "✏️ <b>Manual Bank Details Entry</b>\n"
            message += "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n"
            message += "⚠️ <b>Automatic validation is unavailable.</b>\n\n"
            message += "Please enter your IFSC code manually.\n"
            message += "We'll save it without automatic validation.\n\n"
            message += "📝 <b>Format:</b> 11 characters (4 letters + 7 alphanumeric)\n"
            message += "💡 <b>Example:</b> SBIN0001234\n\n"
            message += "⚠️ <b>Important:</b> Please ensure the IFSC code is correct as it will be used for withdrawals.\n\n"
            message += "Send /cancel to cancel this process."

            await query.edit_message_text(message, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in _handle_manual_bank_entry: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)
    
    async def _handle_promotion_report(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle promotion report callback (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id
        chat_id = query.message.chat_id
        message_id = query.message.message_id

        try:
            # Check maintenance and ban status
            from services.user_service import UserService
            user_service = UserService()

            if await user_service.is_maintenance_mode():
                await query.edit_message_text(
                    "⚙️ Bot is currently under maintenance, please try again later.",
                    parse_mode='HTML'
                )
                return

            user = await user_service.get_user(user_id)
            if not user:
                await query.edit_message_text(
                    "❌ <b>User Not Found</b>\n\nPlease send /start first.",
                    parse_mode='HTML'
                )
                return

            if user.get('banned', False):
                await query.edit_message_text(
                    "🚫 You are banned from using this bot.",
                    parse_mode='HTML'
                )
                return

            # Get promotion reports (matching PHP exactly)
            promotion_reports = user.get('promotion_report', [])

            # Format message exactly like PHP
            message = "👥 Promotion report :\n\n"

            if not promotion_reports:
                message += "❌ No referrals yet."
            else:
                for report in promotion_reports:
                    referred_user_name = report.get('referred_user_name', 'Unknown')
                    amount_got = report.get('amount_got', 0)
                    message += f"💵 {referred_user_name} : ₹{amount_got}\n"

            from telegram import InlineKeyboardButton, InlineKeyboardMarkup

            # Back button exactly like PHP
            back_keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️Back', callback_data='myWallet')]
            ])

            # Use smart navigation to handle media-to-text transitions
            await self._smart_navigate_to_text_message(query, message, back_keyboard)

        except Exception as e:
            logger.error(f"Error in _handle_promotion_report: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)
    
    async def _handle_withdrawal_record(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle withdrawal record callback (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id
        chat_id = query.message.chat_id
        message_id = query.message.message_id

        try:
            # Check maintenance and ban status
            from services.user_service import UserService
            user_service = UserService()

            if await user_service.is_maintenance_mode():
                await query.edit_message_text(
                    "⚙️ Bot is currently under maintenance, please try again later.",
                    parse_mode='HTML'
                )
                return

            user = await user_service.get_user(user_id)
            if not user:
                await query.edit_message_text(
                    "❌ <b>User Not Found</b>\n\nPlease send /start first.",
                    parse_mode='HTML'
                )
                return

            if user.get('banned', False):
                await query.edit_message_text(
                    "🚫 You are banned from using this bot.",
                    parse_mode='HTML'
                )
                return

            # Get withdrawal reports
            withdrawal_reports = user.get('withdrawal_reports', [])

            if not withdrawal_reports:
                message = "📋 <b>Withdrawal Records</b>\n\n"
                message += "❌ No withdrawal records found.\n\n"
                message += "You haven't made any withdrawal requests yet."

                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('💸 Make Withdrawal', callback_data='cashOut')],
                    [InlineKeyboardButton('↩️ Back to Wallet', callback_data='myWallet')]
                ])

                await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')
                return

            # Format withdrawal records
            message = "📋 <b>Withdrawal Records</b>\n\n"

            # Sort by date (newest first)
            sorted_reports = sorted(withdrawal_reports, key=lambda x: x.get('created_at', 0), reverse=True)

            # Show last 10 records
            recent_reports = sorted_reports[:10]

            for i, record in enumerate(recent_reports, 1):
                amount = record.get('amount', 0)
                status = record.get('status', 'Unknown')
                date = record.get('date', 'Unknown')

                # Format status with emoji
                if status == 'Passed':
                    status_display = '✅ Approved'
                elif status == 'Failed':
                    status_display = '❌ Rejected'
                elif status == 'Under review':
                    status_display = '⏳ Pending'
                else:
                    status_display = f'📋 {status}'

                message += f"{i}. ₹{amount} - {date} - {status_display}\n"

            if len(withdrawal_reports) > 10:
                message += f"\n... and {len(withdrawal_reports) - 10} more records"

            # Add summary
            total_successful = sum(r.get('amount', 0) for r in withdrawal_reports if r.get('status') == 'Passed')
            total_pending = sum(r.get('amount', 0) for r in withdrawal_reports if r.get('status') == 'Under review')

            message += f"\n\n📊 <b>Summary:</b>\n"
            message += f"✅ Total Approved: ₹{total_successful}\n"
            message += f"⏳ Total Pending: ₹{total_pending}"

            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('💸 New Withdrawal', callback_data='cashOut')],
                [InlineKeyboardButton('↩️ Back to Wallet', callback_data='myWallet')]
            ])

            # Use smart navigation to handle media-to-text transitions
            await self._smart_navigate_to_text_message(query, message, keyboard)

        except Exception as e:
            logger.error(f"Error in _handle_withdrawal_record: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)
    
    async def _handle_claim_bonus(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle claim bonus callback"""
        # Will be implemented in bonus handlers
        await update.callback_query.answer("🚧 Feature coming soon!", show_alert=True)
    
    async def _handle_extra_rewards(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle extra rewards callback (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id
        chat_id = query.message.chat_id
        message_id = query.message.message_id

        try:
            # Check maintenance and ban status
            from services.user_service import UserService
            user_service = UserService()

            if await user_service.is_maintenance_mode():
                await query.edit_message_text(
                    "⚙️ Bot is currently under maintenance, please try again later.",
                    parse_mode='HTML'
                )
                return

            user = await user_service.get_user(user_id)
            if user and user.get('banned', False):
                await query.edit_message_text(
                    "🚫 You are banned from using this bot.",
                    parse_mode='HTML'
                )
                return

            message = "🎁 <b>Extra Rewards</b>\n\n"
            message += "Earn additional income through tasks and gift codes!\n\n"
            message += "Choose an option below:"

            from telegram import InlineKeyboardButton, InlineKeyboardMarkup

            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('📋 Task Rewards', callback_data='taskRewards')],
                [InlineKeyboardButton('🎫 Redeem Gift Code', callback_data='redeemGiftCode')],
                [InlineKeyboardButton('🏆 Level Rewards', callback_data='levelRewards')],
                [InlineKeyboardButton('↩️ Back to Wallet', callback_data='myWallet')]
            ])

            # Use smart navigation to handle media-to-text transitions
            await self._smart_navigate_to_text_message(query, message, keyboard)

        except Exception as e:
            logger.error(f"Error in _handle_extra_rewards: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def _handle_task_rewards(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle task rewards callback (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id
        chat_id = query.message.chat_id
        message_id = query.message.message_id

        try:
            # Check maintenance and ban status
            from services.user_service import UserService
            user_service = UserService()

            if await user_service.is_maintenance_mode():
                await query.edit_message_text(
                    "⚙️ Bot is currently under maintenance, please try again later.",
                    parse_mode='HTML'
                )
                return

            user = await user_service.get_user(user_id)
            if user and user.get('banned', False):
                await query.edit_message_text(
                    "🚫 You are banned from using this bot.",
                    parse_mode='HTML'
                )
                return

            from services.task_service import TaskService
            task_service = TaskService()

            active_tasks = await task_service.get_active_tasks()

            if not active_tasks:
                message = "📋 <b>Task Rewards</b>\n\n"
                message += "❌ No tasks available at the moment. Please check back later."

                from telegram import InlineKeyboardButton, InlineKeyboardMarkup

                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back', callback_data='extraRewards')]
                ])

                await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')
                return

            message = "📋 <b>Available Tasks</b>\n\n"
            message += "Select a task to view details and earn rewards:\n\n"

            from telegram import InlineKeyboardButton, InlineKeyboardMarkup

            keyboard_buttons = []

            for task in active_tasks:
                task_name = task['name']
                reward_amount = task['reward_amount']

                keyboard_buttons.append([
                    InlineKeyboardButton(
                        f"💰 {task_name} - ₹{reward_amount}",
                        callback_data=f"viewTask_{task['task_id']}"
                    )
                ])

            keyboard_buttons.append([
                InlineKeyboardButton('↩️ Back', callback_data='extraRewards')
            ])

            keyboard = InlineKeyboardMarkup(keyboard_buttons)

            # Use smart navigation to handle media-to-text transitions
            await self._smart_navigate_to_text_message(query, message, keyboard)

        except Exception as e:
            logger.error(f"Error in _handle_task_rewards: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def _handle_redeem_gift_code(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle redeem gift code callback (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            # Check maintenance and ban status
            from services.user_service import UserService
            user_service = UserService()

            if await user_service.is_maintenance_mode():
                await query.edit_message_text(
                    "⚙️ Bot is currently under maintenance, please try again later.",
                    parse_mode='HTML'
                )
                return

            user = await user_service.get_user(user_id)
            if user and user.get('banned', False):
                await query.edit_message_text(
                    "🚫 You are banned from using this bot.",
                    parse_mode='HTML'
                )
                return

            message = "🎫 <b>Redeem Gift Code</b>\n\n"
            message += "Enter your gift code to redeem rewards:\n\n"
            message += "Send /cancel to cancel the process."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set user session for gift code redemption
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'redeem_gift_code')

        except Exception as e:
            logger.error(f"Error in _handle_redeem_gift_code: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def _handle_view_all_gift_codes(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle view all gift codes callback (admin only)"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            from utils.helpers import is_admin
            if not is_admin(user_id):
                await query.answer("❌ Access denied.", show_alert=True)
                return

            from services.gift_code_service import GiftCodeService
            gift_code_service = GiftCodeService()

            message = await gift_code_service.generate_admin_gift_codes_message()
            keyboard = await gift_code_service.generate_admin_gift_codes_keyboard()

            await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in _handle_view_all_gift_codes: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)
    
    async def _handle_level_rewards(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle level rewards callback (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id
        chat_id = query.message.chat_id
        message_id = query.message.message_id

        try:
            # Check maintenance and ban status
            from services.user_service import UserService
            user_service = UserService()

            if await user_service.is_maintenance_mode():
                await query.answer(
                    "⚙️ Bot is currently under maintenance, please try again later.",
                    show_alert=True
                )
                return

            user = await user_service.get_user(user_id)
            if not user:
                await query.answer(
                    "❌ User not found. Please send /start first.",
                    show_alert=True
                )
                return

            if user.get('banned', False):
                await query.answer(
                    "🚫 You are banned from using this bot.",
                    show_alert=True
                )
                return

            # Check if level rewards are enabled
            from services.level_rewards_service import LevelRewardsService
            level_rewards_service = LevelRewardsService()

            if not await level_rewards_service.is_level_rewards_enabled():
                message = "🏆 <b>Level Rewards</b>\n\n"
                message += "❌ Level rewards system is currently not available.\n\n"
                message += "Please check back later."

                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back', callback_data='extraRewards')]
                ])

                await self._smart_navigate_to_text_message(query, message, keyboard)
                return

            # Generate level rewards message and keyboard
            message = await level_rewards_service.generate_level_rewards_message(user_id)
            keyboard = await level_rewards_service.generate_level_rewards_keyboard(user_id)

            await self._smart_navigate_to_text_message(query, message, keyboard)

        except Exception as e:
            logger.error(f"Error in _handle_level_rewards: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)
    
    async def _handle_withdrawal_method_selection(self, update: Update, context: ContextTypes.DEFAULT_TYPE, method: str):
        """Handle withdrawal method selection (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id
        chat_id = query.message.chat_id
        message_id = query.message.message_id

        try:
            # Check maintenance and ban status
            from services.user_service import UserService
            user_service = UserService()

            if await user_service.is_maintenance_mode():
                await query.edit_message_text(
                    "⚙️ Bot is currently under maintenance, please try again later.",
                    parse_mode='HTML'
                )
                return

            user = await user_service.get_user(user_id)
            if not user:
                await query.edit_message_text(
                    "❌ <b>User Not Found</b>\n\nPlease send /start first.",
                    parse_mode='HTML'
                )
                return

            if user.get('banned', False):
                await query.edit_message_text(
                    "🚫 You are banned from using this bot.",
                    parse_mode='HTML'
                )
                return

            # Update withdrawal method
            from services.withdrawal_service import WithdrawalService
            withdrawal_service = WithdrawalService()

            if await withdrawal_service.update_account_info(user_id, 'withdrawal_method', method):
                # Show success message and redirect to account info
                if method == 'usdt':
                    message = "✅ <b>Withdrawal Method Updated</b>\n\n"
                    message += "🔄 Switched to USDT (Binance ID)\n\n"
                    message += "💾 Your previous bank details have been preserved.\n"
                    message += "⚙️ Please set your Binance ID to complete the setup.\n\n"
                    message += "💡 You can switch back to bank account anytime."
                else:
                    message = "✅ <b>Withdrawal Method Updated</b>\n\n"
                    message += "🔄 Switched to Bank Account\n\n"
                    message += "💾 Your previous USDT details have been preserved.\n"
                    message += "⚙️ Please complete your bank account details.\n\n"
                    message += "💡 You can switch back to USDT anytime."

                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('⚙️ Complete Setup', callback_data='setAccountInfo')],
                    [InlineKeyboardButton('↩️ Back to Wallet', callback_data='myWallet')]
                ])

                await query.edit_message_text(
                    message,
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )

                await query.answer("✅ Withdrawal method updated successfully!", show_alert=True)

            else:
                await query.answer("❌ Failed to update withdrawal method. Please try again.", show_alert=True)

        except Exception as e:
            logger.error(f"Error in _handle_withdrawal_method_selection: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)
    
    async def _handle_withdraw_amount(self, update: Update, context: ContextTypes.DEFAULT_TYPE, amount: int):
        """Handle withdraw amount selection (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id
        chat_id = query.message.chat_id
        message_id = query.message.message_id

        try:
            # Check maintenance and ban status
            from services.user_service import UserService
            from services.maintenance_service import MaintenanceService

            user_service = UserService()
            maintenance_service = MaintenanceService()

            # Check general maintenance first
            if await maintenance_service.is_general_maintenance_active():
                await query.edit_message_text(
                    "⚙️ <b>System Under Maintenance</b>\n\n"
                    "The bot is currently under maintenance. Please try again later.\n\n"
                    "Thank you for your patience! 🙏",
                    parse_mode='HTML'
                )
                return

            user = await user_service.get_user(user_id)
            if not user:
                await query.edit_message_text(
                    "❌ <b>User Not Found</b>\n\nPlease send /start first.",
                    parse_mode='HTML'
                )
                return

            if user.get('banned', False):
                await query.edit_message_text(
                    "🚫 You are banned from using this bot.",
                    parse_mode='HTML'
                )
                return

            # Process withdrawal request
            from services.withdrawal_service import WithdrawalService
            withdrawal_service = WithdrawalService()

            account_info = user.get('account_info', {})
            withdrawal_method = account_info.get('withdrawal_method', 'bank')

            # Check specific withdrawal type maintenance
            if withdrawal_method == 'usdt' and await maintenance_service.is_usdt_withdrawal_maintenance_active():
                await query.edit_message_text(
                    "⚙️ <b>USDT Withdrawals Under Maintenance</b>\n\n"
                    "USDT withdrawals are currently under maintenance. Please try again later.\n\n"
                    "💡 You can switch to bank withdrawals in your account settings if needed.\n\n"
                    "Thank you for your patience! 🙏",
                    parse_mode='HTML'
                )
                return
            elif withdrawal_method == 'bank' and await maintenance_service.is_bank_withdrawal_maintenance_active():
                await query.edit_message_text(
                    "⚙️ <b>Bank Withdrawals Under Maintenance</b>\n\n"
                    "Bank withdrawals are currently under maintenance. Please try again later.\n\n"
                    "💡 You can switch to USDT withdrawals in your account settings if needed.\n\n"
                    "Thank you for your patience! 🙏",
                    parse_mode='HTML'
                )
                return

            result = await withdrawal_service.process_withdrawal_request(
                user_id, amount, withdrawal_method
            )

            if result['success']:
                # Show success message
                success_message = result['message']

                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('💰 My Wallet', callback_data='myWallet')],
                    [InlineKeyboardButton('📋 Withdrawal Record', callback_data='withdrawalRecord')]
                ])

                await query.edit_message_text(
                    success_message,
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )

                await query.answer("✅ Withdrawal request submitted successfully!", show_alert=True)

            else:
                # Show error message
                error_message = f"❌ <b>Withdrawal Failed</b>\n\n{result['error']}"

                if 'missing_fields' in result:
                    error_message += "\n\nPlease complete your account information first."
                    keyboard = InlineKeyboardMarkup([
                        [InlineKeyboardButton('⚙️ Set Account Info', callback_data='setAccountInfo')],
                        [InlineKeyboardButton('↩️ Back to Cash Out', callback_data='cashOut')]
                    ])
                else:
                    keyboard = InlineKeyboardMarkup([
                        [InlineKeyboardButton('↩️ Back to Cash Out', callback_data='cashOut')],
                        [InlineKeyboardButton('💰 My Wallet', callback_data='myWallet')]
                    ])

                await query.edit_message_text(
                    error_message,
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )

                await query.answer("❌ " + result['error'], show_alert=True)

        except Exception as e:
            logger.error(f"Error in _handle_withdraw_amount: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)
    
    async def _handle_set_account_field(self, update: Update, context: ContextTypes.DEFAULT_TYPE, field: str):
        """Handle set account field (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id
        chat_id = query.message.chat_id
        message_id = query.message.message_id

        try:
            # Check maintenance and ban status
            from services.user_service import UserService
            user_service = UserService()

            if await user_service.is_maintenance_mode():
                await query.edit_message_text(
                    "⚙️ Bot is currently under maintenance, please try again later.",
                    parse_mode='HTML'
                )
                return

            user = await user_service.get_user(user_id)
            if not user:
                await query.edit_message_text(
                    "❌ <b>User Not Found</b>\n\nPlease send /start first.",
                    parse_mode='HTML'
                )
                return

            if user.get('banned', False):
                await query.edit_message_text(
                    "🚫 You are banned from using this bot.",
                    parse_mode='HTML'
                )
                return

            # Map field names to display names and instructions
            field_info = {
                'name': {
                    'display': 'Full Name',
                    'instruction': 'Enter your full name as it appears on your bank account:',
                    'example': 'Example: John Doe'
                },
                'ifsc': {
                    'display': 'IFSC Code',
                    'instruction': 'Enter your bank IFSC code:',
                    'example': 'Example: SBIN0001234'
                },
                'email': {
                    'display': 'Email Address',
                    'instruction': 'Enter your email address:',
                    'example': 'Example: <EMAIL>'
                },
                'account_number': {
                    'display': 'Account Number',
                    'instruction': 'Enter your bank account number:',
                    'example': 'Example: *********0'
                },
                'mobile_number': {
                    'display': 'Mobile Number',
                    'instruction': 'Enter your mobile number (with country code):',
                    'example': 'Example: +************'
                },
                'binance_id': {
                    'display': 'Binance ID',
                    'instruction': 'Enter your Binance ID for USDT withdrawals:',
                    'example': 'Example: *********'
                },
                'USDTAddress': {  # Legacy compatibility for existing buttons
                    'display': 'Binance ID',
                    'instruction': 'Enter your Binance ID for USDT withdrawals:',
                    'example': 'Example: *********'
                }
            }

            if field not in field_info:
                await query.answer("❌ Invalid field.", show_alert=True)
                return

            info = field_info[field]

            message = f"⚙️ <b>Set {info['display']}</b>\n\n"
            message += f"📝 {info['instruction']}\n\n"
            message += f"💡 {info['example']}\n\n"
            message += "Send /cancel to cancel the process."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set user session for account field setting
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()

            if field == 'mobile_number':
                await session_handlers.set_user_session(user_id, 'set_mobile_number')
            elif field == 'binance_id' or field == 'USDTAddress':  # Handle both new and legacy field names
                await session_handlers.set_user_session(user_id, 'set_binance_id')
            else:
                await session_handlers.set_user_session(user_id, f'set_account_{field}')

        except Exception as e:
            logger.error(f"Error in _handle_set_account_field: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)
    
    async def _handle_view_task(self, update: Update, context: ContextTypes.DEFAULT_TYPE, task_id: str):
        """Handle view task (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id
        chat_id = query.message.chat_id
        message_id = query.message.message_id

        try:
            # Check maintenance and ban status
            from services.user_service import UserService
            user_service = UserService()

            if await user_service.is_maintenance_mode():
                await query.edit_message_text(
                    "⚙️ Bot is currently under maintenance, please try again later.",
                    parse_mode='HTML'
                )
                return

            user = await user_service.get_user(user_id)
            if user and user.get('banned', False):
                await query.edit_message_text(
                    "🚫 You are banned from using this bot.",
                    parse_mode='HTML'
                )
                return

            from services.task_service import TaskService
            task_service = TaskService()

            task = await task_service.get_task_by_id(task_id)
            if not task or task['status'] != 'active':
                await query.answer("❌ Task not found or no longer available.", show_alert=True)
                return

            # Check if user has already submitted this task
            user_submission = await task_service.check_user_task_submission(user_id, task_id)

            message = f"📋 <b>{task['name']}</b>\n\n"
            message += f"📝 <b>Description:</b>\n{task['description']}\n\n"
            message += f"💰 <b>Reward:</b> ₹{task['reward_amount']}\n\n"

            from telegram import InlineKeyboardButton, InlineKeyboardMarkup

            if user_submission:
                message += "✅ <b>Status:</b> Already submitted\n"
                message += "⏳ Please wait for admin approval."

                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to Tasks', callback_data='taskRewards')]
                ])
            else:
                message += "📸 <b>Instructions:</b>\n"
                message += "Complete the task and submit a screenshot as proof."

                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('📸 Submit Screenshot', callback_data=f"submitTask_{task_id}")],
                    [InlineKeyboardButton('↩️ Back to Tasks', callback_data='taskRewards')]
                ])

            # Handle media if task has media_url
            if task.get('media_url') and task['media_url'].strip():
                try:
                    # Send photo with caption
                    await query.message.delete()
                    await context.bot.send_photo(
                        chat_id=chat_id,
                        photo=task['media_url'],
                        caption=message,
                        parse_mode='HTML',
                        reply_markup=keyboard
                    )
                except Exception as e:
                    logger.error(f"Error sending task media: {e}")
                    # Fallback to text message - handle potential message edit failures
                    try:
                        await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')
                    except Exception as edit_error:
                        logger.error(f"Error editing message after media failure: {edit_error}")
                        # Final fallback - send new message
                        try:
                            await context.bot.send_message(
                                chat_id=chat_id,
                                text=message,
                                reply_markup=keyboard,
                                parse_mode='HTML'
                            )
                        except Exception as send_error:
                            logger.error(f"Error sending fallback message: {send_error}")
                            await query.answer("❌ Failed to display task. Please try again.", show_alert=True)
            else:
                # Handle text-only message
                try:
                    await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')
                except Exception as e:
                    logger.error(f"Error editing message text: {e}")
                    # Fallback - send new message
                    try:
                        await context.bot.send_message(
                            chat_id=chat_id,
                            text=message,
                            reply_markup=keyboard,
                            parse_mode='HTML'
                        )
                    except Exception as send_error:
                        logger.error(f"Error sending fallback text message: {send_error}")
                        await query.answer("❌ Failed to display task. Please try again.", show_alert=True)

        except Exception as e:
            logger.error(f"Error in _handle_view_task: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def _handle_submit_task(self, update: Update, context: ContextTypes.DEFAULT_TYPE, task_id: str):
        """Handle submit task (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id
        chat_id = query.message.chat_id

        try:
            # Check maintenance and ban status
            from services.user_service import UserService
            user_service = UserService()

            if await user_service.is_maintenance_mode():
                try:
                    await query.edit_message_text(
                        "⚙️ Bot is currently under maintenance, please try again later.",
                        parse_mode='HTML'
                    )
                except Exception as e:
                    logger.error(f"Error editing maintenance message: {e}")
                    await query.answer("⚙️ Bot is currently under maintenance, please try again later.", show_alert=True)
                return

            user = await user_service.get_user(user_id)
            if user and user.get('banned', False):
                try:
                    await query.edit_message_text(
                        "🚫 You are banned from using this bot.",
                        parse_mode='HTML'
                    )
                except Exception as e:
                    logger.error(f"Error editing ban message: {e}")
                    await query.answer("🚫 You are banned from using this bot.", show_alert=True)
                return

            from services.task_service import TaskService
            task_service = TaskService()

            task = await task_service.get_task_by_id(task_id)
            if not task or task['status'] != 'active':
                await query.answer("❌ Task not found or no longer available.", show_alert=True)
                return

            # Check if user has already submitted this task
            user_submission = await task_service.check_user_task_submission(user_id, task_id)
            if user_submission:
                await query.answer("❌ You have already submitted this task.", show_alert=True)
                return

            message = f"📸 <b>Submit Screenshot for: {task['name']}</b>\n\n"
            message += "📋 <b>Instructions:</b>\n"
            message += "1. Complete the task as described\n"
            message += "2. Take a clear screenshot as proof\n"
            message += "3. Send the screenshot to this chat\n\n"
            message += "⚠️ <b>Important:</b>\n"
            message += "• Screenshots are reviewed manually\n"
            message += "• Fake or edited screenshots will be rejected\n"
            message += "• False proofs may lead to a ban\n\n"
            message += "💰 <b>Reward:</b> ₹{} will be credited once approved\n\n".format(task['reward_amount'])
            message += "Send /cancel to cancel the submission."

            # Use smart navigation to handle media-to-text transitions
            await self._smart_navigate_to_text_message(query, message, None, context)

            # Set user session for task submission
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'submit_task_screenshot', {'task_id': task_id})

        except Exception as e:
            logger.error(f"Error in _handle_submit_task: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)
    
    async def _handle_claim_level_bonus(self, update: Update, context: ContextTypes.DEFAULT_TYPE, level: int):
        """Handle claim level bonus"""
        # Will be implemented in level rewards handlers
        await update.callback_query.answer("🚧 Feature coming soon!", show_alert=True)
    
    async def _handle_withdrawal_approval_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE, target_user_id: str, action: str):
        """Handle withdrawal approval/rejection (matching PHP version exactly)"""
        query = update.callback_query
        admin_id = query.from_user.id
        chat_id = query.message.chat_id
        message_id = query.message.message_id

        try:
            from utils.helpers import is_admin
            if not is_admin(admin_id):
                await query.answer("❌ Access denied.", show_alert=True)
                return

            # Convert target_user_id to int
            try:
                user_id = int(target_user_id)
            except ValueError:
                await query.answer("❌ Invalid user ID.", show_alert=True)
                return

            from services.withdrawal_service import WithdrawalService
            from services.user_service import UserService
            from models.withdrawal import WithdrawalModel

            withdrawal_service = WithdrawalService()
            user_service = UserService()

            # Get user data
            user = await user_service.get_user(user_id)
            if not user:
                await query.answer("❌ User not found.", show_alert=True)
                return

            amount = user.get('withdraw_under_review', 0)
            if amount <= 0:
                await query.answer("❌ No pending withdrawal for this user.", show_alert=True)
                return

            # Process the action
            if action == 'approve':
                success = await withdrawal_service.approve_withdrawal(user_id, admin_id)
                if success:
                    # Update the message to show approval
                    admin_name = query.from_user.first_name or "Admin"
                    processed_date = get_current_date()

                    updated_message = WithdrawalModel.format_updated_admin_message(
                        user, amount, "APPROVED", admin_name, processed_date
                    )

                    try:
                        await query.edit_message_text(
                            updated_message,
                            parse_mode='HTML'
                        )
                    except Exception as e:
                        logger.error(f"Error updating admin message: {e}")

                    await query.answer("✅ Withdrawal approved successfully!", show_alert=True)
                else:
                    await query.answer("❌ Failed to approve withdrawal. Please try again.", show_alert=True)

            elif action == 'reject':
                success = await withdrawal_service.reject_withdrawal(user_id, admin_id)
                if success:
                    # Update the message to show rejection
                    admin_name = query.from_user.first_name or "Admin"
                    processed_date = get_current_date()

                    updated_message = WithdrawalModel.format_updated_admin_message(
                        user, amount, "REJECTED", admin_name, processed_date
                    )

                    try:
                        await query.edit_message_text(
                            updated_message,
                            parse_mode='HTML'
                        )
                    except Exception as e:
                        logger.error(f"Error updating admin message: {e}")

                    await query.answer("❌ Withdrawal rejected successfully!", show_alert=True)
                else:
                    await query.answer("❌ Failed to reject withdrawal. Please try again.", show_alert=True)

            else:
                await query.answer("❌ Invalid action.", show_alert=True)

        except Exception as e:
            logger.error(f"Error in _handle_withdrawal_approval_callback: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def _handle_claim_gift(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle claim gift callback (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id
        chat_id = query.message.chat_id
        message_id = query.message.message_id

        try:
            # Check maintenance and ban status
            from services.user_service import UserService
            user_service = UserService()

            if await user_service.is_maintenance_mode():
                await query.answer(
                    "⚙️ Bot is currently under maintenance, please try again later.",
                    show_alert=True
                )
                return

            user = await user_service.get_user(user_id)
            if not user:
                await query.answer(
                    "❌ User not found. Please send /start first.",
                    show_alert=True
                )
                return

            if user.get('banned', False):
                await query.answer(
                    "🚫 You are banned from using this bot.",
                    show_alert=True
                )
                return

            # Get current gift broadcast
            from services.admin_service import AdminService
            admin_service = AdminService()

            gift_broadcast = await admin_service.get_current_gift_broadcast()

            if not gift_broadcast:
                await query.answer(
                    "❌ No active gift broadcast found.",
                    show_alert=True
                )
                return

            # Check if user has already claimed this gift
            gift_id = gift_broadcast.get('broadcast_id', '')
            claimed_gifts = user.get('claimed_gifts', [])

            if gift_id in claimed_gifts:
                await query.answer(
                    "❌ You have already claimed this gift.",
                    show_alert=True
                )
                return

            # Check channel membership
            channel_id = gift_broadcast.get('channel_id')
            channel_username = gift_broadcast.get('username', '')

            if channel_id or channel_username:
                try:
                    # Check if user is member of the channel
                    if channel_username:
                        check_channel = f"@{channel_username}" if not channel_username.startswith('@') else channel_username
                    else:
                        check_channel = channel_id

                    member = await context.bot.get_chat_member(check_channel, user_id)

                    if member.status in ['left', 'kicked']:
                        await query.answer(
                            "❌ You must join the channel first to claim the gift.",
                            show_alert=True
                        )
                        return

                except Exception as e:
                    logger.error(f"Error checking channel membership: {e}")
                    # If we can't check membership, allow the claim
                    pass

            # Award the gift
            amount = gift_broadcast.get('amount', 0)

            if amount > 0:
                # Add amount to user balance
                if await user_service.update_user_balance(user_id, amount, 'add'):
                    # Mark gift as claimed
                    if not await user_service.add_claimed_gift(user_id, gift_id):
                        # Rollback balance if we can't mark as claimed
                        await user_service.update_user_balance(user_id, amount, 'subtract')
                        await query.answer(
                            "❌ Error claiming gift. Please try again.",
                            show_alert=True
                        )
                        return

                    # Show success message
                    success_message = f"🎉 <b>Gift Claimed Successfully!</b>\n\n"
                    success_message += f"💰 <b>Amount:</b> ₹{amount}\n"
                    success_message += f"💳 <b>New Balance:</b> ₹{user.get('balance', 0) + amount}\n\n"
                    success_message += f"Thank you for joining our channel!"

                    keyboard = InlineKeyboardMarkup([
                        [InlineKeyboardButton('💰 My Wallet', callback_data='myWallet')],
                        [InlineKeyboardButton('🎁 More Rewards', callback_data='extraRewards')]
                    ])

                    await query.edit_message_text(
                        success_message,
                        reply_markup=keyboard,
                        parse_mode='HTML'
                    )

                    await query.answer("🎉 Gift claimed successfully!", show_alert=True)

                else:
                    await query.answer(
                        "❌ Error updating balance. Please try again.",
                        show_alert=True
                    )
            else:
                await query.answer(
                    "❌ Invalid gift amount.",
                    show_alert=True
                )

        except Exception as e:
            logger.error(f"Error in _handle_claim_gift: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def _handle_preview_withdrawal_tax(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle preview withdrawal tax (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            from utils.helpers import is_admin
            if not is_admin(user_id):
                await query.answer("❌ Access denied.", show_alert=True)
                return

            from services.admin_service import AdminService
            admin_service = AdminService()

            # Get withdrawal settings
            withdrawal_settings = await admin_service.get_withdrawal_settings(user_id)

            tax_type = withdrawal_settings.get('tax_type', 'none')
            tax_amount = withdrawal_settings.get('tax_amount', 0)
            tax_percentage = withdrawal_settings.get('tax_percentage', 0)

            message = "📊 <b>Withdrawal Tax Preview</b>\n\n"

            if tax_type == 'none':
                message += "❌ <b>No tax configured</b>\n\n"
                message += "Examples:\n"
                message += "• User withdraws ₹100 → Receives ₹100\n"
                message += "• User withdraws ₹500 → Receives ₹500\n"
                message += "• User withdraws ₹1000 → Receives ₹1000"
            elif tax_type == 'fixed':
                message += f"💰 <b>Fixed Tax:</b> ₹{tax_amount} per withdrawal\n\n"
                message += "Examples:\n"
                message += f"• User withdraws ₹100 → Receives ₹{100 - tax_amount}\n"
                message += f"• User withdraws ₹500 → Receives ₹{500 - tax_amount}\n"
                message += f"• User withdraws ₹1000 → Receives ₹{1000 - tax_amount}"
            elif tax_type == 'percentage':
                message += f"📊 <b>Percentage Tax:</b> {tax_percentage}% of withdrawal amount\n\n"
                message += "Examples:\n"
                message += f"• User withdraws ₹100 → Receives ₹{100 - (100 * tax_percentage / 100):.2f}\n"
                message += f"• User withdraws ₹500 → Receives ₹{500 - (500 * tax_percentage / 100):.2f}\n"
                message += f"• User withdraws ₹1000 → Receives ₹{1000 - (1000 * tax_percentage / 100):.2f}"

            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Back to Settings', callback_data='withdrawal_settings')],
                [InlineKeyboardButton('🏠 Admin Panel', callback_data='admin')]
            ])

            await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in _handle_preview_withdrawal_tax: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def _handle_check_subscription(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle check subscription callback (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            from services.force_subscription_service import ForceSubscriptionService
            force_sub_service = ForceSubscriptionService()

            # Check user subscriptions
            subscription_status = await force_sub_service.check_user_subscriptions(user_id)

            if subscription_status['all_subscribed']:
                # User is subscribed to all channels
                await query.answer("✅ Subscription verified! You can now use the bot.", show_alert=True)

                # Show main menu
                from handlers.message_handlers import MessageHandlers
                message_handlers = MessageHandlers()
                await message_handlers._show_main_menu(query.message.chat_id, user_id, context)

                # Delete the subscription message
                try:
                    await query.message.delete()
                except Exception:
                    pass  # Ignore if message can't be deleted

            else:
                # User still not subscribed to some channels
                unsubscribed_channels = subscription_status['unsubscribed_channels']

                message, keyboard = await force_sub_service.get_subscription_message_and_keyboard(unsubscribed_channels)

                # Send subscription message as NEW message (matching PHP behavior)
                await context.bot.send_message(
                    chat_id=query.message.chat_id,
                    text=message,
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
                await query.answer("❌ Please join all required channels first.", show_alert=True)

        except Exception as e:
            logger.error(f"Error in _handle_check_subscription: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def _handle_claim_level_bonus(self, update: Update, context: ContextTypes.DEFAULT_TYPE, level: int):
        """Handle claim level bonus callback (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id
        chat_id = query.message.chat_id

        try:
            # Check maintenance and ban status
            from services.user_service import UserService
            user_service = UserService()

            if await user_service.is_maintenance_mode():
                await query.answer(
                    "⚙️ Bot is currently under maintenance, please try again later.",
                    show_alert=True
                )
                return

            user = await user_service.get_user(user_id)
            if not user:
                await query.answer(
                    "❌ User not found. Please send /start first.",
                    show_alert=True
                )
                return

            if user.get('banned', False):
                await query.answer(
                    "🚫 You are banned from using this bot.",
                    show_alert=True
                )
                return

            # Check if level rewards are enabled
            from services.level_rewards_service import LevelRewardsService
            level_rewards_service = LevelRewardsService()

            if not await level_rewards_service.is_level_rewards_enabled():
                await query.answer(
                    "❌ Level rewards system is currently disabled.",
                    show_alert=True
                )
                return

            # Claim the level bonus
            result = await level_rewards_service.claim_level_bonus(user_id, level)

            if result['success']:
                # Show success message
                from models.level_rewards import LevelRewardsModel

                success_message = LevelRewardsModel.format_level_claim_success_message(
                    result['level'], result['amount'], user.get('balance', 0) + result['amount']
                )

                keyboard = LevelRewardsModel.create_claim_success_keyboard()

                await query.edit_message_text(
                    success_message,
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )

                await query.answer("🎉 Level bonus claimed successfully!", show_alert=True)

                # Check if user has more eligible levels
                eligible_levels = await level_rewards_service.get_user_eligible_levels(user_id)
                if eligible_levels:
                    next_level = min(eligible_levels)
                    config = await level_rewards_service.get_level_rewards_config()
                    next_bonus = float(config['bonus_amounts'][next_level - 1])

                    follow_up_message = LevelRewardsModel.format_follow_up_message(
                        next_level, next_bonus
                    )

                    from utils.helpers import send_safe_message
                    from telegram import Bot
                    bot = Bot(settings.BOT_TOKEN)

                    await send_safe_message(bot, chat_id, follow_up_message, parse_mode='HTML')

            else:
                await query.answer(f"❌ {result['message']}", show_alert=True)

        except Exception as e:
            logger.error(f"Error in _handle_claim_level_bonus: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def _handle_configure_level_settings(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle configure level settings callback (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id
        chat_id = query.message.chat_id

        try:
            from utils.helpers import is_admin
            if not is_admin(user_id):
                await query.answer("❌ Access denied.", show_alert=True)
                return

            message = "⚙️ <b>Configure Level Settings</b>\n\n"
            message += "Send the new configuration in the following format:\n\n"
            message += "<b>Format:</b>\n"
            message += "<code>referrals: 1,5,10,15,20,25\n"
            message += "bonuses: 2,10,15,20,25,30</code>\n\n"
            message += "📝 <b>Example:</b>\n"
            message += "<code>referrals: 2,8,15,25,35,50\n"
            message += "bonuses: 5,15,25,35,50,75</code>\n\n"
            message += "💡 <b>Rules:</b>\n"
            message += "• Must specify exactly 6 levels\n"
            message += "• Referral requirements should be in ascending order\n"
            message += "• All values must be positive numbers\n\n"
            message += "Send /cancel to cancel the configuration."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set user session for level configuration
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'configure_level_settings')

        except Exception as e:
            logger.error(f"Error in _handle_configure_level_settings: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def _smart_navigate_to_text_message(self, query, message: str, keyboard, context=None):
        """Smart navigation to handle media-to-text transitions (matching PHP version)"""
        try:
            # Try to edit the message first
            await query.edit_message_text(
                message,
                reply_markup=keyboard,
                parse_mode='HTML'
            )
        except Exception as e:
            logger.error(f"Error editing message in smart navigation: {e}")
            # If editing fails (e.g., message has media), delete and send new
            try:
                chat_id = query.message.chat_id
                await query.message.delete()
                if context and hasattr(context, 'bot'):
                    await context.bot.send_message(
                        chat_id=chat_id,
                        text=message,
                        reply_markup=keyboard,
                        parse_mode='HTML'
                    )
                else:
                    # Fallback using query.answer for critical errors
                    await query.answer("❌ Failed to display message. Please try again.", show_alert=True)
            except Exception as e2:
                logger.error(f"Error in smart navigation fallback: {e2}")
                await query.answer("❌ Failed to display message. Please try again.", show_alert=True)

    async def _handle_confirm_reset_account(self, update: Update, context: ContextTypes.DEFAULT_TYPE, target_user_id: int):
        """Handle confirmation of user account reset"""
        query = update.callback_query
        admin_user_id = query.from_user.id

        try:
            from utils.helpers import is_admin
            if not is_admin(admin_user_id):
                await query.answer("❌ Access denied.", show_alert=True)
                return

            # Get user service
            from services.user_service import UserService
            user_service = UserService()

            # Get target user
            target_user = await user_service.get_user(target_user_id)
            if not target_user:
                await query.answer("❌ User not found.", show_alert=True)
                return

            # Reset user account details
            success = await user_service.reset_account_details(target_user_id)

            if success:
                target_name = target_user.get('first_name', 'Unknown')
                admin_name = query.from_user.first_name or 'Admin'

                message = f"✅ <b>Account Reset Successful</b>\n\n"
                message += f"👤 <b>User:</b> {target_name} (ID: {target_user_id})\n"
                message += f"👨‍💼 <b>Reset by:</b> {admin_name}\n\n"
                message += f"🔄 <b>Actions Completed:</b>\n"
                message += f"• Cleared withdrawal method selection\n"
                message += f"• Removed all account details\n"
                message += f"• User can now set up withdrawal method again\n\n"
                message += f"📝 The user will be prompted to choose their withdrawal method and enter details when they next access their wallet."

                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('🔙 Back to Admin Panel', callback_data='admin')]
                ])

                await query.edit_message_text(
                    message,
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )

                await query.answer("✅ User account details reset successfully!", show_alert=True)

                # Log the action
                logger.info(f"Admin {admin_user_id} ({admin_name}) reset account details for user {target_user_id} ({target_name})")

            else:
                await query.answer("❌ Failed to reset user account details. Please try again.", show_alert=True)

        except Exception as e:
            logger.error(f"Error in _handle_confirm_reset_account: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    # ==================== GIFT BROADCAST VERIFICATION HANDLER ====================

    async def _handle_gift_broadcast_verification(self, update: Update, context: ContextTypes.DEFAULT_TYPE, broadcast_id: str) -> None:
        """Handle gift broadcast verification and reward distribution"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            await query.answer()

            # Process user participation using GiftBroadcastService
            from services.gift_broadcast_service import GiftBroadcastService
            gift_service = GiftBroadcastService()

            success, message, reward_amount = await gift_service.process_user_participation(broadcast_id, user_id)

            if success:
                # Show success message
                await query.edit_message_text(
                    f"🎉 <b>Congratulations!</b>\n\n{message}\n\n💰 Your new balance has been updated!",
                    parse_mode='HTML'
                )
            else:
                # Show error message with retry option if applicable
                if "must join the channel first" in message.lower():
                    # User hasn't joined yet, show join button again
                    join_message, keyboard = await gift_service.generate_join_message_and_keyboard(broadcast_id)
                    if keyboard:
                        await query.edit_message_text(
                            f"⚠️ {message}\n\nPlease join the channel first, then click verify again.",
                            reply_markup=keyboard,
                            parse_mode='HTML'
                        )
                    else:
                        await query.edit_message_text(
                            f"⚠️ {message}",
                            parse_mode='HTML'
                        )
                else:
                    # Other error, just show message
                    await query.edit_message_text(
                        f"⚠️ {message}",
                        parse_mode='HTML'
                    )

        except Exception as e:
            logger.error(f"Error in _handle_gift_broadcast_verification: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while verifying your participation. Please try again later.",
                parse_mode='HTML'
            )
