#!/usr/bin/env python3
"""
OTP API Diagnostic Tool
Tests the actual OTP API endpoint to understand response formats and delivery status
"""

import sys
import os
import asyncio
import logging
import requests
import json
from datetime import datetime

# Add the python directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_otp_api_configuration():
    """Test OTP API configuration"""
    print("🧪 Testing OTP API Configuration...")
    
    try:
        from config.settings import settings
        
        print(f"✅ OTP_API_URL: {settings.OTP_API_URL}")
        print(f"✅ OTP_API_KEY: {settings.OTP_API_KEY[:10]}..." if settings.OTP_API_KEY else "❌ OTP_API_KEY: Not set")
        
        if not settings.OTP_API_KEY:
            print("❌ OTP API key is not configured!")
            return False
        
        return True
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

async def test_api_endpoint_connectivity():
    """Test basic connectivity to the OTP API endpoint"""
    print("🧪 Testing API Endpoint Connectivity...")
    
    try:
        from config.settings import settings
        
        # Test basic connectivity without parameters
        base_url = settings.OTP_API_URL.split('?')[0]  # Remove any existing parameters
        
        print(f"Testing connectivity to: {base_url}")
        
        try:
            response = requests.get(base_url, timeout=10)
            print(f"✅ Endpoint reachable - Status: {response.status_code}")
            print(f"✅ Response headers: {dict(response.headers)}")
            print(f"✅ Response content: {response.text[:500]}")
            return True
        except requests.Timeout:
            print("❌ Timeout connecting to API endpoint")
            return False
        except requests.ConnectionError:
            print("❌ Connection error - endpoint may be down")
            return False
        except Exception as e:
            print(f"❌ Error connecting to endpoint: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Connectivity test failed: {e}")
        return False

async def test_otp_api_call():
    """Test actual OTP API call with the problematic number"""
    print("🧪 Testing OTP API Call...")
    
    try:
        from config.settings import settings
        
        # Test with the specific number that's not receiving OTP
        test_number = "+918949098157"
        test_otp = "1234"  # Test OTP
        
        api_url = f"{settings.OTP_API_URL}?API={settings.OTP_API_KEY}&PHONE={test_number}&OTP={test_otp}"
        
        print(f"Testing OTP API call to: {test_number}")
        print(f"API URL (masked): {settings.OTP_API_URL}?API=***&PHONE={test_number}&OTP={test_otp}")
        
        try:
            response = requests.get(api_url, timeout=30)
            
            print(f"✅ API Response Status: {response.status_code}")
            print(f"✅ API Response Headers: {dict(response.headers)}")
            print(f"✅ API Response Content: {response.text}")
            print(f"✅ API Response Length: {len(response.text)} characters")
            
            # Try to parse response as JSON
            try:
                json_response = response.json()
                print(f"✅ JSON Response: {json.dumps(json_response, indent=2)}")
            except:
                print("ℹ️ Response is not JSON format")
            
            # Analyze response content for success/failure indicators
            response_text = response.text.lower()
            
            success_indicators = ['success', 'sent', 'delivered', 'ok', 'true']
            failure_indicators = ['error', 'failed', 'invalid', 'false', 'blocked', 'rejected']
            
            found_success = any(indicator in response_text for indicator in success_indicators)
            found_failure = any(indicator in response_text for indicator in failure_indicators)
            
            print(f"✅ Success indicators found: {found_success}")
            print(f"✅ Failure indicators found: {found_failure}")
            
            if response.status_code == 200:
                if found_failure:
                    print("⚠️ WARNING: Status 200 but response contains failure indicators!")
                    print("⚠️ This suggests the API call succeeded but SMS delivery failed")
                elif found_success:
                    print("✅ Status 200 with success indicators - likely delivered")
                else:
                    print("⚠️ Status 200 but unclear delivery status from response content")
            
            return True
            
        except requests.Timeout:
            print("❌ Timeout during OTP API call")
            return False
        except requests.ConnectionError:
            print("❌ Connection error during OTP API call")
            return False
        except Exception as e:
            print(f"❌ Error during OTP API call: {e}")
            return False
            
    except Exception as e:
        print(f"❌ OTP API call test failed: {e}")
        return False

async def test_different_number_formats():
    """Test different number formats to see if format is the issue"""
    print("🧪 Testing Different Number Formats...")
    
    try:
        from config.settings import settings
        
        # Test different formats of the same number
        test_formats = [
            "+918949098157",    # Original format
            "918949098157",     # Without +
            "8949098157",       # Without country code
            "+91 8949098157",   # With space
            "+91-8949098157",   # With dash
        ]
        
        test_otp = "1234"
        
        for number_format in test_formats:
            print(f"\nTesting format: {number_format}")
            
            api_url = f"{settings.OTP_API_URL}?API={settings.OTP_API_KEY}&PHONE={number_format}&OTP={test_otp}"
            
            try:
                response = requests.get(api_url, timeout=10)
                print(f"  Status: {response.status_code}")
                print(f"  Response: {response.text[:100]}")
                
                # Check for specific error messages about number format
                if 'invalid' in response.text.lower() or 'format' in response.text.lower():
                    print(f"  ⚠️ Possible format issue detected")
                
            except Exception as e:
                print(f"  ❌ Error: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Number format test failed: {e}")
        return False

async def test_api_key_validity():
    """Test if the API key is valid"""
    print("🧪 Testing API Key Validity...")
    
    try:
        from config.settings import settings
        
        # Test with a clearly invalid number to see API key response
        invalid_number = "+911234567890"  # Likely invalid but properly formatted
        test_otp = "1234"
        
        api_url = f"{settings.OTP_API_URL}?API={settings.OTP_API_KEY}&PHONE={invalid_number}&OTP={test_otp}"
        
        print(f"Testing API key with invalid number: {invalid_number}")
        
        try:
            response = requests.get(api_url, timeout=10)
            print(f"✅ Status: {response.status_code}")
            print(f"✅ Response: {response.text}")
            
            # Check for API key related errors
            response_lower = response.text.lower()
            if 'api' in response_lower and ('invalid' in response_lower or 'unauthorized' in response_lower):
                print("❌ API key appears to be invalid or unauthorized")
                return False
            elif 'api' in response_lower and 'valid' in response_lower:
                print("✅ API key appears to be valid")
                return True
            else:
                print("ℹ️ API key validity unclear from response")
                return True
                
        except Exception as e:
            print(f"❌ Error testing API key: {e}")
            return False
            
    except Exception as e:
        print(f"❌ API key test failed: {e}")
        return False

async def analyze_sms_service_status():
    """Analyze the SMS service status and common issues"""
    print("🧪 Analyzing SMS Service Status...")
    
    try:
        print("📋 Common SMS delivery issues:")
        print("  • Carrier blocking (Jio, Airtel, Vi may block promotional SMS)")
        print("  • DND (Do Not Disturb) settings on the number")
        print("  • Number porting or temporary service issues")
        print("  • SMS gateway rate limiting")
        print("  • Invalid or inactive number")
        print("  • International roaming restrictions")
        
        print("\n📋 Debugging steps:")
        print("  1. Try with a different mobile number")
        print("  2. Check if the number has DND enabled")
        print("  3. Verify the number is active and can receive SMS")
        print("  4. Test with numbers from different carriers")
        print("  5. Check SMS service provider status")
        
        return True
        
    except Exception as e:
        print(f"❌ SMS service analysis failed: {e}")
        return False

async def generate_diagnostic_report():
    """Generate a comprehensive diagnostic report"""
    print("📊 Generating Diagnostic Report...")
    
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    report = f"""
OTP API DIAGNOSTIC REPORT
Generated: {timestamp}

ISSUE: User +918949098157 not receiving OTP despite bot showing "OTP sent successfully"

ANALYSIS:
1. The current implementation only checks HTTP status 200
2. Status 200 doesn't guarantee SMS delivery
3. Many SMS APIs return 200 even when SMS fails to deliver
4. Need to parse response content for actual delivery status

RECOMMENDATIONS:
1. Improve response parsing to detect delivery failures
2. Add response content analysis for success/failure indicators
3. Implement retry mechanism for failed deliveries
4. Add user feedback for delivery confirmation
5. Provide alternative verification methods

NEXT STEPS:
1. Update OTP service to parse response content
2. Add delivery status detection
3. Implement better user feedback
4. Add diagnostic logging for troubleshooting
"""
    
    print(report)
    return True

async def main():
    """Run all diagnostic tests"""
    print("🚀 Starting OTP API Diagnostic Tests...\n")
    
    tests = [
        ("OTP API Configuration", test_otp_api_configuration),
        ("API Endpoint Connectivity", test_api_endpoint_connectivity),
        ("OTP API Call", test_otp_api_call),
        ("Different Number Formats", test_different_number_formats),
        ("API Key Validity", test_api_key_validity),
        ("SMS Service Status", analyze_sms_service_status),
        ("Diagnostic Report", generate_diagnostic_report)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"Running {test_name} Test")
        print('='*60)
        
        result = await test_func()
        results.append((test_name, result))
    
    print(f"\n{'='*60}")
    print("DIAGNOSTIC RESULTS SUMMARY")
    print('='*60)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print(f"\nOverall Result: {'✅ ALL TESTS PASSED' if all_passed else '⚠️ ISSUES DETECTED'}")
    
    if not all_passed:
        print("\n🔍 Issues detected that may explain why OTP is not being received")
        print("📋 Check the detailed test results above for specific problems")
    
    return all_passed

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
